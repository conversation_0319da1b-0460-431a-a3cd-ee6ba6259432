
Ceci est l'exemple fourni par Gemini pour donner l'illusion du lever de soleil réaliste. Attention à être vraiment très prudent. Vous pouvez tout à fait vous en inspirer, mais il faut rester prudent. 


Le Code pour votre Application React + GSAP
Maintenant, passons à la partie la plus importante : le code que vous pourrez intégrer dans votre projet.
La meilleure approche pour le halo et le "lens flare" est d'utiliser des éléments CSS purs. C'est plus performant que de manipuler des images et ça nous donne un contrôle total avec GSAP.
Étape 1 : Structure du Composant (AnimatedLandscape.tsx)
Créez un nouveau composant. Il contiendra la logique d'animation et les éléments visuels. Nous utiliserons des ref pour que GSAP puisse cibler les éléments du DOM.
Generated tsx

Voici l'animation générée. Notez que c'est une version simplifiée ; les effets que nous allons ajouter avec CSS et GSAP la rendront bien plus impressionnante.

**Animation du lever de soleil :**
![Animation du lever de soleil](sun_rise_animation.gif)

### 2. Le Code pour votre Application React + GSAP

Maintenant, passons à la partie la plus importante : le code que vous pourrez intégrer dans votre projet.

La meilleure approche pour le halo et le "lens flare" est d'utiliser des éléments CSS purs. C'est plus performant que de manipuler des images et ça nous donne un contrôle total avec GSAP.

#### Étape 1 : Structure du Composant (`AnimatedLandscape.tsx`)

Créez un nouveau composant. Il contiendra la logique d'animation et les éléments visuels. Nous utiliserons des `ref` pour que GSAP puisse cibler les éléments du DOM.

```tsx
// src/components/AnimatedLandscape.tsx
import React, { useLayoutEffect, useRef } from 'react';
import { gsap } from 'gsap';

// Importez vos images
import sunImg from 'public\SUN.png'; // Remplacez par le chemin vers input_file_0.png
import hillsImg from 'public\Background.png'; // Remplacez par le chemin vers une des collines

const AnimatedLandscape: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sunWrapperRef = useRef<HTMLDivElement>(null);
  const sunHaloRef = useRef<HTMLDivElement>(null);
  const lensFlareRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    // gsap.context() est la meilleure pratique pour gérer le cleanup dans React
    const ctx = gsap.context(() => {
      // Animation principale avec une timeline
      const tl = gsap.timeline({
        scrollTrigger: { // Optionnel: déclenchez l'animation au scroll
          trigger: containerRef.current,
          start: 'top bottom',
          end: 'center center',
          scrub: 1.5, // Lie l'animation au scroll
        },
      });

      // 1. Le soleil se lève
      tl.fromTo(
        sunWrapperRef.current,
        { y: '100%' },
        { y: '0%', ease: 'power2.out' }
      );

      // 2. Le halo apparaît et s'intensifie
      tl.fromTo(
        sunHaloRef.current,
        { opacity: 0, scale: 0.5 },
        { opacity: 1, scale: 1, ease: 'power2.inOut' },
        '<' // Démarre en même temps que l'animation précédente
      );
        
      // 3. Le lens flare apparaît avec un léger décalage
      tl.fromTo(
        lensFlareRef.current,
        { opacity: 0, y: '50%', scale: 0.2 },
        { opacity: 1, y: '0%', scale: 1, ease: 'power2.out' },
        '<0.2' // Démarre 0.2s après le début de l'animation précédente
      );

    }, containerRef);

    // Cleanup
    return () => ctx.revert();
  }, []);

  return (
    <div ref={containerRef} className="relative w-full h-screen overflow-hidden">
      {/* Conteneur pour le soleil et ses effets */}
      <div ref={sunWrapperRef} className="absolute z-0 w-1/4 left-1/2 -translate-x-1/2 top-1/2">
        <div className="relative aspect-square">
            {/* EFFET 1: Le Halo lumineux */}
            <div ref={sunHaloRef} className="sun-halo absolute inset-0"></div>
            
            {/* EFFET 2: Le Lens Flare */}
            <div ref={lensFlareRef} className="lens-flare absolute inset-[-200%]"></div>

            {/* L'image du soleil */}
            <img src={sunImg} alt="Sun" className="relative z-10 w-full h-full" />
        </div>
      </div>

      {/* L'image des collines au premier plan */}
      <img
        src={hillsImg}
        alt="Hills"
        className="absolute bottom-0 left-0 z-20 object-cover w-full h-auto"
      />
    </div>
  );
};

export default AnimatedLandscape;
```

#### Étape 2 : Le CSS pour les Effets (`styles.css`)

Ajoutez ce CSS à votre fichier de styles global. J'ai utilisé des variables CSS pour que ce soit facile à personnaliser.

```css
/* public/styles.css ou index.css */

/*
  Nous utilisons filter: drop-shadow pour le halo.
  Contrairement à box-shadow, il respecte la transparence de l'image PNG,
  ce qui donne un halo parfaitement circulaire autour du soleil.
*/
.sun-halo {
  --halo-color: #ffdd00;
  --halo-blur: 50px;
  
  background: var(--halo-color);
  border-radius: 50%;
  filter: drop-shadow(0 0 var(--halo-blur) var(--halo-color));
  transform-origin: center center;
}

/*
  C'est ici que la magie opère pour le "lens flare" style Photoshop.
  On utilise une série de `radial-gradient` pour simuler les reflets de la lentille.
  C'est un pseudo-élément pour ne pas ajouter de div inutile.
  L'animation de son opacité et de sa taille avec GSAP le rendra très réaliste.
*/
.lens-flare::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 50%;
  background: 
    /* Grand halo central doux */
    radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0) 20%),
    /* Reflet bleuâtre subtil */
    radial-gradient(circle at 45% 55%, rgba(180, 210, 255, 0.3) 15%, rgba(180, 210, 255, 0) 40%),
    /* Petits éclats lumineux */
    radial-gradient(circle at 60% 40%, rgba(255, 230, 200, 0.5) 5%, rgba(255, 230, 200, 0) 15%);
  
  transform-origin: center center;
  mix-blend-mode: screen; /* Le mode de fusion est crucial pour un effet réaliste */
}

```

### Comment l'utiliser :

1.  **Copiez le composant** `AnimatedLandscape.tsx` dans votre projet.
2.  **Mettez à jour les chemins** vers vos images (`sun.png`, `hills.png`).
3.  **Copiez le CSS** dans votre fichier de style principal (par ex. `src/index.css`).
4.  **Intégrez le composant** dans votre page principale, par exemple dans `App.tsx`.

Cette solution vous donne une base très solide et performante. Vous pouvez facilement ajuster les couleurs (`--halo-color`), la taille du flou (`--halo-blur`) et bien sûr, peaufiner l'animation GSAP (durée, easing, déclencheurs) pour qu'elle corresponde parfaitement à la dynamique de votre application. L'utilisation de `scrollTrigger` est optionnelle mais peut créer un effet très immersif si votre page est scrollable.



