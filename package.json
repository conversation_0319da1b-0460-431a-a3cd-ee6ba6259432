{"name": "pointeuse-d-activite-pro", "private": true, "version": "1.0.0", "type": "module", "description": "Application de pointeuse d'activité pour freelances et intérimaires", "keywords": ["timetracker", "freelance", "firebase", "react", "typescript"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "build:prod": "npm run type-check && vite build", "check-audio": "node scripts/check-audio-files.js", "build:check": "npm run build && npm run check-audio"}, "dependencies": {"firebase": "^12.0.0", "gsap": "^3.13.0", "react": "^19.1.0", "react-dom": "^19.1.0", "suncalc": "^1.9.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/suncalc": "^1.9.2", "@vitejs/plugin-react": "^4.7.0", "terser": "^5.43.1", "typescript": "~5.7.2", "vite": "^6.2.0"}}